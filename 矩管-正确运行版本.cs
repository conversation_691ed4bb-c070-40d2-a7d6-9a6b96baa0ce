/// <summary>
/// 钢矩管
/// </summary>
[Serializable] //序列化标识，所有图元必须有这个标签
[DbElement("钢矩管", MajorType.CurtainWall)]
public class SteelTubePlanD : DbElement
{ 
    #region  简化（以下边中点为基点-conpts中只储存下边和几何中点
        #region 几何参数
        /// <summary>
        /// 矩管宽度
        /// </summary>
        private double _width = 60;
        /// <summary>
        /// 矩管宽度
        /// </summary>
        [Category("几何参数"), DisplayName("宽度"), Description("矩管宽度"), ReadOnly(false)]
        public double Width
        {
            get { return _width; }
            set
            {
                if (_width == value) return;
                TransManager.Instance().Push(a => Width = a, _width);
                _width = value;
                // 宽度修改时，下边中点不变，左右同时扩大
                _isGeometryOperation = false;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 矩管高度
        /// </summary>
        private double _height = 120;
        /// <summary>
        /// 矩管高度
        /// </summary>
        [Category("几何参数"), DisplayName("高度"), Description("矩管高度"), ReadOnly(false)]
        public double Height
        {
            get { return _height; }
            set
            {
                if (_height == value) return;
                TransManager.Instance().Push(a => Height = a, _height);
                _height = value;
                // 高度修改时，下边中点不变，向上扩大
                _isGeometryOperation = false;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 壁厚
        /// </summary>
        private double _thickness = 3;
        /// <summary>
        /// 壁厚
        /// </summary>
        [Category("几何参数"), DisplayName("壁厚"), Description("矩管壁厚"), ReadOnly(false)]
        public double Thickness
        {
            get { return _thickness; }
            set
            {
                if (_thickness == value) return;
                TransManager.Instance().Push(a => Thickness = a, _thickness);
                _thickness = value;
                // 壁厚改变时，圆角半径会自动重新计算
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 弯角半径
        /// </summary>
        private double _radius;
        /// <summary>
        /// 弯角半径 - 根据壁厚自动计算
        /// </summary>
        [Category("几何参数"), DisplayName("弯角半径"), Description("外弯角半径（根据壁厚自动计算）"), ReadOnly(true)]
        public double Radius
        {
            get { return CalculateRadius(); }
        }

        /// <summary>
        /// 根据壁厚自动计算圆角半径
        /// 规则：
        /// - 当壁厚t≤3时，r=2*t
        /// - 当3<t≤6时，r=2.5*t  
        /// - 当6<t≤10时，r=3*t
        /// - 当t>10时，r=3*t
        /// </summary>
        /// <returns>计算得到的圆角半径</returns>
        private double CalculateRadius()
        {
            if (_thickness <= 3)
            {
                return 2 * _thickness;
            }
            else if (_thickness <= 6)
            {
                return 2.5 * _thickness;
            }
            else if (_thickness <= 10)
            {
                return 3 * _thickness;
            }
            else
            {
                return 3 * _thickness;
            }
        }

        /// <summary> 
        /// 截面旋转角度
        /// </summary>
        public double _hoAngle;
        /// <summary>
        /// 截面旋转角度
        /// </summary>
        [Category("几何参数"), DisplayName("截面旋转"), Description("截面旋转角度"), ReadOnly(false)]
        public double HoAngle
        {
            get => _hoAngle;
            set
            {
                if (Math.Abs(_hoAngle - value) < 0.001) return;
                TransManager.Instance().Push(a => HoAngle = a, _hoAngle);

                // 如果有变换矩阵，需要将新角度整合到变换矩阵中
                if (_hasTransform)
                {
                    // 先将当前角度整合到变换矩阵中
                    IntegrateRotationIntoTransform();

                    // 计算角度差值
                    double angleDiff = value - _hoAngle;

                    // 创建新的旋转矩阵
                    double angleRad = angleDiff * Math.PI / 180.0;
                    double cos = Math.Cos(angleRad);
                    double sin = Math.Sin(angleRad);

                    Matrix3D rotationMatrix = new Matrix3D(
                        cos, -sin, 0, 0,
                        sin, cos, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1
                    );

                    // 将新的旋转矩阵整合到变换矩阵中
                    _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);

                    // 重置角度，因为角度信息已经整合到变换矩阵中
                    _hoAngle = 0;
                }
                else
                {
                    // 没有变换矩阵时，直接设置角度
                    _hoAngle = value;
                }

                // 角度修改时，重新计算控制点
                _isGeometryOperation = false; // 参数修改，不是几何操作

                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary> 
        /// 是否填充
        /// </summary>
        public bool _ifHatch = true;
        /// <summary>
        /// 是否填充
        /// </summary>
        [Category("显示属性"), DisplayName("是否填充"), Description("钢矩管截面是否显示填充"), ReadOnly(false)]
        public bool IfHatch
        {
            get { return _ifHatch; }
            set
            {
                if (_ifHatch == value) return;
                TransManager.Instance().Push(a => IfHatch = a, _ifHatch);
                _ifHatch = value;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 是否显示中心线
        /// </summary>
        private bool _ifShowCenterLine = false;
        /// <summary>
        /// 是否显示中心线
        /// </summary>
        [Category("显示属性"), DisplayName("显示中心线"), Description("是否显示矩管中心十字线"), ReadOnly(false)]
        public bool IfShowCenterLine
        {
            get { return _ifShowCenterLine; }
            set
            {
                if (_ifShowCenterLine == value) return;
                TransManager.Instance().Push(a => IfShowCenterLine = a, _ifShowCenterLine);
                _ifShowCenterLine = value;
                ActCutCal2D3D();
            }
        }

        #endregion

        #region 核心属性
        /// <summary>
        /// 插入点（用户指定的基准点）- 固定在矩管下边中点往下30mm处
        /// </summary>
        public DbPt _insertPt = new DbPt();

        /// <summary>
        /// 下边中点（矩管的基准点）- 从插入点向上偏移30mm，存储在ConPts[0]
        /// </summary>
        public DbPt _bottomCenterPt = new DbPt();

        /// <summary>
        /// 几何中心点 - 基于下边中点计算，用于内部计算和引用，存储在ConPts[1]
        /// </summary>
        public DbPt _centerPt = new DbPt();

        /// <summary>
        /// 是否正在进行几何操作（移动、旋转、镜像）
        /// true: 几何操作，直接使用操作后的控制点
        /// false: 参数修改，需要清空控制点重新计算
        /// </summary>
        private bool _isGeometryOperation = false;

        /// <summary>
        /// 变换矩阵 - 用于跟踪矩管的完整变换状态（镜像）
        /// </summary>
        private Matrix3D _transformMatrix = Matrix3D.Identity;

        /// <summary>
        /// 是否应用了变换矩阵
        /// </summary>
        private bool _hasTransform = false;

        #endregion

        #region 构造函数
        /// <summary>
        /// 无参构造函数，每个图元必须保留无参构造函数
        /// </summary>
        public SteelTubePlanD()
        {
            _if3D = false;
            LayerSet("幕墙龙骨");
        }

        /// <summary>
        /// 标准构造函数
        /// </summary>
        /// <param name="insertPt">插入点</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="thickness">壁厚</param>
        /// <param name="hoAngle">截面旋转角度</param>
        public SteelTubePlanD(DbPt insertPt, double width = 60.0, double height = 120.0, double thickness = 3.0,
                               double hoAngle = 0.0)
        {
            _if3D = false;
            LayerSet("幕墙龙骨");
            _width = width;
            _height = height;
            _thickness = thickness;
            _hoAngle = hoAngle;
            _insertPt = insertPt;
            _ifHatch = true;

            // 初始化时需要计算下边中点和几何中心
            _isGeometryOperation = false; // 初始化不是几何操作

            // 计算下边中点和几何中心点，控制点的创建留给Activate()方法
            CalcuBottomCenter();
            CalcuPtcenter();
        }

        #endregion

        #region 核心计算方法
        /// <summary>
        /// 计算下边中点 - 基于插入点固定偏移30mm
        /// 插入点固定在矩管下边中点往下30mm处，不再进行插入点和几何中点的判定
        /// </summary>
        public void CalcuBottomCenter()
        {
            // 插入点位置固定：矩管下边中点往下偏移30mm处
            // 因此下边中点 = 插入点向上偏移30mm
            _bottomCenterPt = _insertPt.Move(0, 30);
        }

        /// <summary>
        /// 计算几何中心点 - 基于下边中点
        /// </summary>
        public void CalcuPtcenter()
        {
            // 几何中心 = 下边中点向上偏移高度的一半
            _centerPt = _bottomCenterPt.Move(0, _height / 2.0);
        }

        /// <summary>
        /// 计算控制点 - 只保留下边中点和几何中心点
        /// ConPts[0]: 下边中点，ConPts[1]: 几何中心点
        /// 简化版：几何中心按标准偏移计算，所有变换统一应用
        /// </summary>
        public void CalcuPts()
        {
            // 清除所有控制点
            ConPts.Clear();

            // ConPts[0]: 下边中点
            DbPt bottomCenter = _bottomCenterPt.EleCopy();
            bottomCenter.PtType = 2; // 标记为边中点
            ConPts.Add(bottomCenter);

            // ConPts[1]: 几何中心点 - 标准偏移计算
            double halfHeight = _height / 2.0;
            DbPt geometryCenter = new DbPt(bottomCenter.X, bottomCenter.Y + halfHeight);
            geometryCenter.PtType = 1; // 标记为几何中心
            ConPts.Add(geometryCenter);

            // 更新几何中心点引用
            _centerPt = geometryCenter.EleCopy();

            // 应用所有变换（旋转+镜像）
            ApplyAllTransforms();
        }



        /// <summary>
        /// 应用所有变换（旋转+镜像）到控制点
        /// </summary>
        private void ApplyAllTransforms()
        {
            if (ConPts.Count <= 1) return;

            DbPt center = ConPts[0]; // 使用下边中点作为变换中心

            // 对几何中心点应用变换
            if (ConPts.Count > 1)
            {
                // 修复：先应用变换矩阵（镜像等变换），再应用旋转
                // 这样确保变换顺序正确
                if (_hasTransform)
                {
                    ApplyTransformToPoint(ConPts[1], center);
                }

                // 然后应用旋转变换（如果有旋转角度）
                if (Math.Abs(_hoAngle) > 0.001)
                {
                    double angleRad = _hoAngle * Math.PI / 180.0;
                    ConPts[1].RotateSelf(center, angleRad);
                }
            }
        }

        /// <summary>
        /// 获取变换后的方向向量 - 支持任意直线镜像
        /// 通过计算标准点经过完整变换后的位置来推导方向向量
        /// </summary>
        /// <param name="standardDirection">标准方向向量</param>
        /// <returns>变换后的方向向量</returns>
        private DbPt GetTransformedDirection(DbPt standardDirection)
        {
            // 修复：直接通过变换矩阵计算方向向量，避免累积误差
            if (_hasTransform)
            {
                // 直接应用变换矩阵到方向向量
                double transformedX = _transformMatrix.M11 * standardDirection.X + _transformMatrix.M12 * standardDirection.Y;
                double transformedY = _transformMatrix.M21 * standardDirection.X + _transformMatrix.M22 * standardDirection.Y;

                DbPt transformedDirection = new DbPt(transformedX, transformedY);

                // 如果还有旋转角度，继续应用旋转
                if (Math.Abs(_hoAngle) > 0.001)
                {
                    double angleRad = _hoAngle * Math.PI / 180.0;
                    double cos = Math.Cos(angleRad);
                    double sin = Math.Sin(angleRad);

                    double finalX = transformedDirection.X * cos - transformedDirection.Y * sin;
                    double finalY = transformedDirection.X * sin + transformedDirection.Y * cos;

                    transformedDirection.X = finalX;
                    transformedDirection.Y = finalY;
                }

                return transformedDirection;
            }
            else if (Math.Abs(_hoAngle) > 0.001)
            {
                // 只有旋转，没有镜像
                double angleRad = _hoAngle * Math.PI / 180.0;
                double cos = Math.Cos(angleRad);
                double sin = Math.Sin(angleRad);

                return new DbPt(
                    standardDirection.X * cos - standardDirection.Y * sin,
                    standardDirection.X * sin + standardDirection.Y * cos
                );
            }
            else
            {
                // 没有任何变换
                return standardDirection.EleCopy();
            }
        }


        /// <summary>
        /// 获取外轮廓点集 - 基于镜像后的局部坐标系重新计算
        /// </summary>
        /// <returns>包含弧形中点的外轮廓点集</returns>
        private List<DbPt> GetOuterPoints()
        {
            List<DbPt> points = new List<DbPt>();

            // 获取镜像后的局部坐标系方向向量
            DbPt rightDirection = GetTransformedDirection(new DbPt(1, 0)); // X方向（右方向）
            DbPt upDirection = GetTransformedDirection(new DbPt(0, 1));    // Y方向（上方向）

            double halfWidth = _width / 2.0;
            double halfHeight = _height / 2.0;

            // 使用镜像后的下边中点和变换后的方向向量计算四个角点
            DbPt bottomCenter = _bottomCenterPt.EleCopy();

            // 修复：正确计算四个角点，确保镜像后的几何体方向正确
            DbPt[] corners = new DbPt[]
            {
                // 左下：下边中点 - 右方向*半宽
                new DbPt(bottomCenter.X - rightDirection.X * halfWidth,
                         bottomCenter.Y - rightDirection.Y * halfWidth),

                // 右下：下边中点 + 右方向*半宽
                new DbPt(bottomCenter.X + rightDirection.X * halfWidth,
                         bottomCenter.Y + rightDirection.Y * halfWidth),

                // 右上：右下点 + 上方向*高度
                new DbPt(bottomCenter.X + rightDirection.X * halfWidth + upDirection.X * _height,
                         bottomCenter.Y + rightDirection.Y * halfWidth + upDirection.Y * _height),

                // 左上：左下点 + 上方向*高度
                new DbPt(bottomCenter.X - rightDirection.X * halfWidth + upDirection.X * _height,
                         bottomCenter.Y - rightDirection.Y * halfWidth + upDirection.Y * _height),
            };

            // 如果没有圆弧（半径为0），直接返回四个角点
            if (CalculateRadius() < 0.001)
            {
                points.AddRange(corners);

                // 确保点集是逆时针方向
                EnsureCounterClockwise(points);
                return points;
            }

            // 如果有圆角，需要生成圆角点集
            double radius = CalculateRadius();
            if (radius > 0.001)
            {
                // 为每个角添加圆弧点
                for (int i = 0; i < 4; i++)
                {
                    DbPt currentCorner = corners[i];
                    DbPt prevCorner = corners[(i + 3) % 4]; // 上一个角点
                    DbPt nextCorner = corners[(i + 1) % 4]; // 下一个角点

                    // 计算从当前角点到相邻角点的方向向量
                    DbLine l1 = new DbLine(currentCorner, prevCorner);
                    DbLine l2 = new DbLine(currentCorner, nextCorner);

                    DbPt dirPre = l1.PtEnd - l1.PtSt;
                    DbPt dirNext = l2.PtEnd - l2.PtSt;
                    dirPre.Normalize2D();
                    dirNext.Normalize2D();

                    // 计算倒角三个关键点
                    DbPt arcStart = new DbPt(currentCorner.X + dirPre.X * radius, currentCorner.Y + dirPre.Y * radius);
                    DbPt arcEnd = new DbPt(currentCorner.X + dirNext.X * radius, currentCorner.Y + dirNext.Y * radius);
                    DbPt arcCenter = new DbPt(currentCorner.X + (dirPre.X + dirNext.X) * radius,
                                             currentCorner.Y + (dirPre.Y + dirNext.Y) * radius);

                    // 使用现成方法获取弧形中点
                    DbLine arc = GMath.GetArcByStCenEnd(arcStart, arcCenter, arcEnd);
                    DbPt arcMid = arc.PtMid.EleCopy();
                    arcMid.PtType = 1;

                    // 添加圆弧点
                    points.Add(arcStart);
                    points.Add(arcMid);
                    points.Add(arcEnd);
                }
            }
            else
            {
                // 无圆角时直接添加角点
                points.AddRange(corners);
            }

            // 确保点集是逆时针方向
            EnsureCounterClockwise(points);
            return points;
        }

        /// <summary>
        /// 确保点集是逆时针方向
        /// </summary>
        /// <param name="points">点集</param>
        private void EnsureCounterClockwise(List<DbPt> points)
        {
            if (points.Count < 3) return;

            // 计算点集的有向面积（使用叉积）
            double signedArea = 0;
            for (int i = 0; i < points.Count; i++)
            {
                int j = (i + 1) % points.Count;
                signedArea += (points[j].X - points[i].X) * (points[j].Y + points[i].Y);
            }

            // 如果有向面积为正，说明是顺时针，需要反转
            if (signedArea > 0)
            {
                points.Reverse();
            }
        }


        /// <summary>
        /// 将点集转换为线段
        /// </summary>
        /// <param name="points">包含弧形中点的点集</param>
        private void ConvertPointsToLines(List<DbPt> points)
        {
            if (points == null || points.Count < 3) return;

            for (int i = 0; i < points.Count; i++)
            {
                DbPt currentPt = points[i];
                DbPt nextPt = points[(i + 1) % points.Count];

                if (nextPt.PtType == 1) // 下一个点是圆弧中点
                {
                    // 创建圆弧线段
                    DbPt endPt = points[(i + 2) % points.Count];
                    DbLine arcLine = new DbLine(currentPt.EleCopy(), endPt.EleCopy(), nextPt.EleCopy());
                    Lines.Add(arcLine);
                    i++; // 跳过圆弧中点
                }
                else
                {
                    // 创建直线段
                    DbLine line = new DbLine(currentPt.EleCopy(), nextPt.EleCopy());
                    Lines.Add(line);
                }
            }
        }

        /// <summary>
        /// 添加中心线
        /// </summary>
        private void AddCenterLine()
        {
            DbPt center = _bottomCenterPt.EleCopy(); // 使用下边中点作为变换中心和基准点
            double halfWidth = _width / 2.0 + 10;
            double halfHeight = _height / 2.0 + 10;

            // 添加水平中心线（基于下边中点，向上偏移高度的一半）
            double centerY = center.Y + _height / 2.0;
            DbPt hStart = new DbPt(center.X - halfWidth, centerY);
            DbPt hEnd = new DbPt(center.X + halfWidth, centerY);

            // 添加垂直中心线（基于下边中点）
            DbPt vStart = new DbPt(center.X, center.Y - halfHeight);
            DbPt vEnd = new DbPt(center.X, center.Y + _height + halfHeight);

            //应用旋转（以下边中点为中心）
            if (Math.Abs(_hoAngle) > 0.001)
            {
                double angleRad = _hoAngle * Math.PI / 180.0;
                hStart.RotateSelf(center, angleRad);
                hEnd.RotateSelf(center, angleRad);
                vStart.RotateSelf(center, angleRad);
                vEnd.RotateSelf(center, angleRad);
            }

            // 应用变换矩阵（以下边中点为中心）
            if (_hasTransform)
            {
                ApplyTransformToPoint(hStart, center);
                ApplyTransformToPoint(hEnd, center);
                ApplyTransformToPoint(vStart, center);
                ApplyTransformToPoint(vEnd, center);
            }

            DbLine hLine = new DbLine(hStart, hEnd);
            DbLine vLine = new DbLine(vStart, vEnd);

            hLine.LayerId = PreLayerManage.GetLayerId("通用-非打印");
            vLine.LayerId = PreLayerManage.GetLayerId("通用-非打印");
            hLine.SetStatus(0, 2, 0);
            vLine.SetStatus(0, 2, 0);
            hLine.StyleIndex = 2; // 默认样式
            vLine.StyleIndex = 2; // 默认样式

            Lines.Add(hLine);
            Lines.Add(vLine);
        }
        #endregion

        #region 图元操作方法
        /// <summary>
        /// 图元激活计算 - 智能控制点管理
        /// </summary>
        public override void Activate()
        {
            Hatchs.Clear();
            Lines.Clear();

            // 根据操作类型决定控制点的处理方式
            if (_isGeometryOperation)
            {
                // 几何操作：直接使用操作后的控制点
                if (ConPts.Count > 0)
                {
                    _bottomCenterPt = ConPts[0].EleCopy();
                    ExtractTransformFromControlPoints();
                    // 验证并修正控制点位置
                    ValidateAndCorrectControlPoints();
                }
            }
            else
            {
                // 参数修改或初始化：智能处理
                if (ConPts.Count == 0)
                {
                    // 初始化：基于插入点计算下边中点
                    CalcuBottomCenter();
                }
                else
                {
                    // 参数修改：从当前控制点提取下边中点位置
                    _bottomCenterPt = ConPts[0].EleCopy();
                }

                // 基于当前下边中点重新计算几何中心和控制点
                CalcuPtcenter();      // 计算几何中心
                CalcuPts();           // 计算控制点
            }

            // 获取外轮廓点集
            List<DbPt> outerPoints = GetOuterPoints();

            // 获取内轮廓点集
            List<DbPt> innerPoints = GMath.GetOffsetArcPts(outerPoints, _thickness, true);

            // 转换为线段
            ConvertPointsToLines(outerPoints);
            ConvertPointsToLines(innerPoints);
            // 注意：GetOuterPoints()方法需要修改为基于下边中点计算，而不是几何中心
            // 修改建议：
            // 1. 在GetOuterPoints()中使用_bottomCenterPt作为基准点
            // 2. 计算四个角点时，从下边中点向上偏移height，向两侧偏移width/2
            // 3. 保持其他逻辑不变（圆角、变换等）
            // 添加填充
            if (_ifHatch)
            {
                DbHatch hatch = new DbHatch(Lines, 1, 1);
                hatch.LayerId = PreLayerManage.GetLayerId("填充层");
                Hatchs.Add(hatch);
            }

            // 设置线段属性
            foreach (DbLine line in Lines)
            {
                line.SetStatus(1, 1, 1);
                line.LayerId = PreLayerManage.GetLayerId("幕墙龙骨");
            }

            LayerChange(layerManage.GetLayer(_layerId));

            if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
            if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
            if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

            // 添加中心线（如果需要）
            if (_ifShowCenterLine)
            {
                AddCenterLine();
            }

            EleArea = new DbEleArea(this);
            CalSolid2D();

            // 重置操作状态
            _isGeometryOperation = false; // 重置几何操作标志

            // 调试：验证镜像变换
            #if DEBUG
            if (_hasTransform)
            {
                DebugMirrorTransform();
                TestMirrorGeometry();
            }
            #endif
        }

        /// <summary>
        /// 返回图元被单独选中时显示的提示内容
        /// </summary>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = "简化矩管";
            str2 = $"{_width:F0}×{_height:F0}×{_thickness:F0}";
        }

        /// <summary>
        /// 重写移动 - 参考建筑柱的实现方式，直接操作所有控制点
        /// </summary>
        public override void EleMove(double X, double Y)
        {
            // 直接移动所有控制点（包括几何中心）
            foreach (DbPt pt in ConPts)
            {
                pt.MoveSelf(X, Y);
            }

            // 移动后更新下边中点和几何中心点（保持一致性）
            if (ConPts.Count > 0)
            {
                _bottomCenterPt = ConPts[0].EleCopy(); // ConPts[0] 是下边中点
                if (ConPts.Count > 1)
                {
                    _centerPt = ConPts[1].EleCopy(); // ConPts[1] 是几何中心
                }
            }

            // 移动操作不改变变换矩阵，因为它只是平移

            _isGeometryOperation = true; // 标记为几何操作
            Activate();
        }

        /// <summary>
        /// 图元拉伸控制 - 基于下边中点的拉伸逻辑
        /// 宽度左右同时扩大，高度向上扩大
        /// </summary>
        public override void EleMove_s(double X, double Y)
        {
            if (ConPts == null || ConPts.Count == 0) return;

            // 检查是否有任何控制点被拖拽
            bool anyPointDragged = false;
            for (int i = 0; i < ConPts.Count; i++)
            {
                if (ConPts[i].Status == 1)
                {
                    anyPointDragged = true;
                    break;
                }
            }

            // 如果有控制点被拖拽，执行整体移动
            if (anyPointDragged)
            {
                EleMove(X, Y);
                return;
            }

            _isGeometryOperation = true;
            Activate();
        }

        /// <summary>
        /// 计算考虑变换的本地坐标增量（扩展版CalculateLocalDelta）
        /// </summary>
        /// <param name="globalX">全局X方向拖拽距离</param>
        /// <param name="globalY">全局Y方向拖拽距离</param>
        /// <param name="isVertical">true表示计算垂直方向，false表示水平方向</param>
        /// <returns>本地坐标系的距离</returns>
        private double CalculateLocalDeltaWithTransform(double globalX, double globalY, bool isVertical)
        {
            // 创建全局移动向量
            DbPt globalDelta = new DbPt(globalX, globalY);

            // 计算标准方向
            DbPt standardDirection;
            if (isVertical)
            {
                // 垂直方向
                standardDirection = new DbPt(0, 1);
            }
            else
            {
                // 水平方向
                standardDirection = new DbPt(1, 0);
            }

            // 应用变换得到实际方向
            DbPt actualDirection;
            if (_hasTransform)
            {
                // 有变换矩阵时，直接应用变换矩阵（包含了旋转和镜像信息）
                actualDirection = new DbPt(
                    _transformMatrix.M11 * standardDirection.X + _transformMatrix.M12 * standardDirection.Y,
                    _transformMatrix.M21 * standardDirection.X + _transformMatrix.M22 * standardDirection.Y
                );
            }
            else if (Math.Abs(_hoAngle) > 0.001)
            {
                // 没有变换矩阵时，只应用旋转角度
                double angleRad = _hoAngle * Math.PI / 180.0;
                if (isVertical)
                {
                    // 垂直方向：考虑旋转角度的垂直方向
                    actualDirection = new DbPt(-Math.Sin(angleRad), Math.Cos(angleRad));
                }
                else
                {
                    // 水平方向：考虑旋转角度的水平方向
                    actualDirection = new DbPt(Math.Cos(angleRad), Math.Sin(angleRad));
                }
            }
            else
            {
                // 没有任何变换时，使用标准方向
                actualDirection = standardDirection;
            }

            // 计算全局移动向量在实际方向上的投影
            double dotProduct = globalDelta.X * actualDirection.X + globalDelta.Y * actualDirection.Y;
            double actualDirectionLength = Math.Sqrt(actualDirection.X * actualDirection.X + actualDirection.Y * actualDirection.Y);

            // 返回投影长度（带符号）
            return actualDirectionLength > 0.001 ? dotProduct / actualDirectionLength : 0;
        }

        /// <summary>
        /// 获取简化的垂直方向（只考虑主要变换）
        /// </summary>
        /// <returns>简化的垂直方向单位向量</returns>
        private DbPt GetSimpleVerticalDirection()
        {
            // 标准垂直方向
            DbPt verticalDir = new DbPt(0, 1);

            // 应用变换
            if (_hasTransform)
            {
                // 有变换矩阵时，直接应用变换矩阵（包含了旋转和镜像信息）
                double transformedX = _transformMatrix.M11 * verticalDir.X + _transformMatrix.M12 * verticalDir.Y;
                double transformedY = _transformMatrix.M21 * verticalDir.X + _transformMatrix.M22 * verticalDir.Y;
                verticalDir.X = transformedX;
                verticalDir.Y = transformedY;
            }
            else if (Math.Abs(_hoAngle) > 0.001)
            {
                // 没有变换矩阵时，只应用旋转角度
                double angleRad = _hoAngle * Math.PI / 180.0;
                double newX = verticalDir.X * Math.Cos(angleRad) - verticalDir.Y * Math.Sin(angleRad);
                double newY = verticalDir.X * Math.Sin(angleRad) + verticalDir.Y * Math.Cos(angleRad);
                verticalDir.X = newX;
                verticalDir.Y = newY;
            }

            verticalDir.Normalize2D();
            return verticalDir;
        }

        /// <summary>
        /// 计算全局拖拽向量在矩管本地坐标系中的投影
        /// </summary>
        /// <param name="globalX">全局X方向拖拽距离</param>
        /// <param name="globalY">全局Y方向拖拽距离</param>
        /// <param name="isVertical">true表示计算垂直方向，false表示水平方向</param>
        /// <returns>本地坐标系的距离</returns>
        private double CalculateLocalDelta(double globalX, double globalY, bool isVertical)
        {
            if (Math.Abs(_hoAngle) < 0.001)
            {
                return isVertical ? globalY : globalX;
            }

            double angleRad = _hoAngle * Math.PI / 180.0;
            double cosAngle = Math.Cos(angleRad);
            double sinAngle = Math.Sin(angleRad);

            if (isVertical)
            {
                // 垂直方向的单位向量（Y轴旋转后）
                return globalX * (-sinAngle) + globalY * cosAngle;
            }
            else
            {
                // 水平方向的单位向量（X轴旋转后）
                return globalX * cosAngle + globalY * sinAngle;
            }
        }

        /// <summary>
        /// 计算点到几何中心的距离（考虑旋转）
        /// </summary>
        /// <param name="point">目标点</param>
        /// <param name="center">几何中心</param>
        /// <param name="isVertical">true表示计算垂直方向距离，false表示水平方向距离</param>
        /// <returns>距离值</returns>
        private double CalculateDistanceFromCenter(DbPt point, DbPt center, bool isVertical)
        {
            if (Math.Abs(_hoAngle) < 0.001)
            {
                // 无旋转时直接计算
                return isVertical ? Math.Abs(point.Y - center.Y) : Math.Abs(point.X - center.X);
            }

            // 有旋转时需要转换到本地坐标系
            double angleRad = _hoAngle * Math.PI / 180.0;
            double cosAngle = Math.Cos(-angleRad); // 反向旋转
            double sinAngle = Math.Sin(-angleRad);

            // 将点转换到本地坐标系（以几何中心为原点）
            double localX = (point.X - center.X) * cosAngle - (point.Y - center.Y) * sinAngle;
            double localY = (point.X - center.X) * sinAngle + (point.Y - center.Y) * cosAngle;

            return isVertical ? Math.Abs(localY) : Math.Abs(localX);
        }

        /// <summary>
        /// 基于下边中点重新计算控制点（简化版）
        /// </summary>
        private void RecalculateFromBottomCenter()
        {
            DbPt bottomCenter = ConPts[0].EleCopy();
            _bottomCenterPt = bottomCenter;

            double halfHeight = _height / 2.0;

            // 重新计算几何中心点位置
            ConPts[1] = new DbPt(bottomCenter.X, bottomCenter.Y + halfHeight) { PtType = 1 }; // 几何中心

            // 更新几何中心点引用
            _centerPt = ConPts[1].EleCopy();

            // 应用所有变换
            ApplyAllTransforms();
        }

        /// <summary>
        /// 对单个点应用变换矩阵
        /// </summary>
        /// <param name="point">要变换的点</param>
        /// <param name="center">变换中心</param>
        private void ApplyTransformToPoint(DbPt point, DbPt center)
        {
            // 将点转换为相对于中心的坐标
            double relativeX = point.X - center.X;
            double relativeY = point.Y - center.Y;

            // 应用变换矩阵
            double transformedX = _transformMatrix.M11 * relativeX + _transformMatrix.M12 * relativeY;
            double transformedY = _transformMatrix.M21 * relativeX + _transformMatrix.M22 * relativeY;

            // 转换回全局坐标
            point.X = center.X + transformedX;
            point.Y = center.Y + transformedY;
        }

        /// <summary>
        /// 验证并修正镜像后的控制点位置
        /// 确保几何中心点相对于下边中点的位置关系正确
        /// </summary>
        private void ValidateAndCorrectControlPoints()
        {
            if (ConPts.Count < 2) return;

            DbPt bottomCenter = ConPts[0];
            DbPt geometryCenter = ConPts[1];

            // 计算当前几何中心到下边中点的距离
            double currentDistance = Math.Sqrt(
                Math.Pow(geometryCenter.X - bottomCenter.X, 2) +
                Math.Pow(geometryCenter.Y - bottomCenter.Y, 2)
            );

            // 期望的距离（高度的一半）
            double expectedDistance = _height / 2.0;

            // 如果距离差异太大，需要修正
            if (Math.Abs(currentDistance - expectedDistance) > 0.1)
            {
                // 计算从下边中点到几何中心的方向向量
                DbPt direction = new DbPt(
                    geometryCenter.X - bottomCenter.X,
                    geometryCenter.Y - bottomCenter.Y
                );

                // 归一化方向向量
                if (currentDistance > 0.001)
                {
                    direction.X /= currentDistance;
                    direction.Y /= currentDistance;

                    // 重新设置几何中心位置
                    ConPts[1].X = bottomCenter.X + direction.X * expectedDistance;
                    ConPts[1].Y = bottomCenter.Y + direction.Y * expectedDistance;

                    // 更新几何中心点引用
                    _centerPt = ConPts[1].EleCopy();
                }
            }
        }

        /// <summary>
        /// 重写旋转 - 参考建筑柱的实现方式，直接旋转所有控制点
        /// </summary>
        public override void EleMove_r(DbPt rotCenter, double angle)
        {
            // 直接旋转所有控制点
            foreach (DbPt pt in ConPts)
            {
                pt.RotateSelf(rotCenter, angle);
            }

            // 旋转后更新下边中点和几何中心点（关键）
            if (ConPts.Count > 0)
            {
                _bottomCenterPt = ConPts[0].EleCopy(); // ConPts[0] 是下边中点
                if (ConPts.Count > 1)
                {
                    _centerPt = ConPts[1].EleCopy(); // ConPts[1] 是几何中心
                }
            }

            // 如果已经有变换矩阵，需要将旋转整合到变换矩阵中
            if (_hasTransform)
            {
                // 先将当前旋转角度整合到变换矩阵中
                IntegrateRotationIntoTransform();

                // 再添加新的旋转
                double angleRad = angle;
                double cos = Math.Cos(angleRad);
                double sin = Math.Sin(angleRad);

                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );

                _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);
                _hoAngle = 0; // 重置角度，因为已经整合到变换矩阵中
            }
            else
            {
                // 没有变换矩阵时，直接更新旋转角度
                _hoAngle += angle * 180 / Math.PI;
            }

            // 标准化角度到 [-180, 180] 范围
            while (_hoAngle > 180) _hoAngle -= 360;
            while (_hoAngle < -180) _hoAngle += 360;

            _isGeometryOperation = true; // 标记为几何操作
            Activate();
        }

        /// <summary>
        /// 重写镜像 - 使用变换矩阵处理镜像变换
        /// </summary>
        public override void EleMove_m(DbLine mirrorLine)
        {
            // 直接镜像所有控制点
            foreach (DbPt pt in ConPts)
            {
                pt.MirrorSelf(mirrorLine);
            }

            // 镜像后更新下边中点和几何中心点（关键修复）
            if (ConPts.Count > 0)
            {
                _bottomCenterPt = ConPts[0].EleCopy(); // ConPts[0] 是下边中点
                if (ConPts.Count > 1)
                {
                    _centerPt = ConPts[1].EleCopy(); // ConPts[1] 是几何中心点
                }
            }

            // 关键修复：将旋转角度信息整合到变换矩阵中
            IntegrateRotationIntoTransform();

            // 更新变换矩阵以包含镜像
            UpdateTransformMatrixForMirror(mirrorLine);

            // 镜像后重置旋转角度，因为旋转信息已经整合到变换矩阵中
            _hoAngle = 0;

            _isGeometryOperation = true; // 标记为几何操作
            Activate();
        }

        /// <summary>
        /// 将旋转角度信息整合到变换矩阵中
        /// </summary>
        private void IntegrateRotationIntoTransform()
        {
            if (Math.Abs(_hoAngle) > 0.001)
            {
                // 创建旋转矩阵
                double angleRad = _hoAngle * Math.PI / 180.0;
                double cos = Math.Cos(angleRad);
                double sin = Math.Sin(angleRad);

                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );

                // 将旋转矩阵整合到现有变换矩阵中
                _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);
                _hasTransform = true;
            }
        }

        /// <summary>
        /// 更新变换矩阵以包含镜像
        /// </summary>
        private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
        {
            // 计算镜像线的方向向量
            DbPt lineDir = mirrorLine.PtEnd - mirrorLine.PtSt;
            lineDir.Normalize2D();

            // 计算镜像线的法向量
            DbPt normal = new DbPt(-lineDir.Y, lineDir.X);

            // 创建镜像矩阵
            // 镜像矩阵 = I - 2 * n * n^T (其中n是单位法向量)
            double nx = normal.X;
            double ny = normal.Y;

            Matrix3D mirrorMatrix = new Matrix3D(
                1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
                -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );

            // 修复：由于控制点已经被镜像操作处理，我们需要将镜像变换组合到现有变换中
            // 镜像变换应该在现有变换之后应用
            _transformMatrix = Matrix3D.Multiply(mirrorMatrix, _transformMatrix);
            _hasTransform = true;
        }

        /// <summary>
        /// 从当前控制点提取变换矩阵
        /// 改进版：通过计算当前几何中心与标准几何中心的变换关系来检测镜像状态
        /// </summary>
        private void ExtractTransformFromControlPoints()
        {
            if (ConPts.Count < 2) return;

            DbPt bottomCenter = ConPts[0]; // 下边中点
            DbPt actualGeometryCenter = ConPts[1]; // 实际几何中心点

            // 计算标准几何中心位置（无变换状态下的位置）
            DbPt standardGeometryCenter = new DbPt(bottomCenter.X, bottomCenter.Y + _height / 2.0);

            // 计算从下边中点到实际几何中心的向量
            DbPt actualVector = new DbPt(
                actualGeometryCenter.X - bottomCenter.X,
                actualGeometryCenter.Y - bottomCenter.Y
            );

            // 计算从下边中点到标准几何中心的向量
            DbPt standardVector = new DbPt(
                standardGeometryCenter.X - bottomCenter.X,
                standardGeometryCenter.Y - bottomCenter.Y
            );

            // 如果两个向量长度相差太大，说明可能有问题，使用默认状态
            double actualLength = Math.Sqrt(actualVector.X * actualVector.X + actualVector.Y * actualVector.Y);
            double standardLength = Math.Sqrt(standardVector.X * standardVector.X + standardVector.Y * standardVector.Y);

            if (Math.Abs(actualLength - standardLength) > 1.0) // 允许1mm的误差
            {
                _transformMatrix = Matrix3D.Identity;
                _hasTransform = false;
                return;
            }

            // 归一化向量
            if (actualLength > 0.001 && standardLength > 0.001)
            {
                actualVector.X /= actualLength;
                actualVector.Y /= actualLength;
                standardVector.X /= standardLength;
                standardVector.Y /= standardLength;

                // 修复：更精确的变换矩阵检测
                // 如果向量几乎相同，没有变换
                if (Math.Abs(actualVector.X - standardVector.X) < 0.001 &&
                    Math.Abs(actualVector.Y - standardVector.Y) < 0.001)
                {
                    _transformMatrix = Matrix3D.Identity;
                    _hasTransform = false;
                }
                else
                {
                    // 通过两个向量构建变换矩阵
                    // 标准向量是 (0, 1)，实际向量是变换后的结果
                    // 我们需要找到将标准向量变换为实际向量的矩阵

                    // 对于2D变换，我们可以通过两个正交向量来构建变换矩阵
                    // 标准X方向: (1, 0), 标准Y方向: (0, 1)
                    // 变换后的Y方向就是actualVector
                    // 变换后的X方向应该是actualVector的垂直向量

                    DbPt transformedY = actualVector;
                    DbPt transformedX = new DbPt(-actualVector.Y, actualVector.X); // 垂直向量

                    // 构建变换矩阵
                    _transformMatrix = new Matrix3D(
                        transformedX.X, transformedY.X, 0, 0,
                        transformedX.Y, transformedY.Y, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1
                    );
                    _hasTransform = true;
                }
            }
            else
            {
                // 向量长度太小，无法判断，使用默认状态
                _transformMatrix = Matrix3D.Identity;
                _hasTransform = false;
            }
        }

        /// <summary>
        /// 调试方法：验证镜像变换是否正确
        /// </summary>
        private void DebugMirrorTransform()
        {
            if (!_hasTransform) return;

            // 输出变换矩阵信息
            System.Diagnostics.Debug.WriteLine($"Transform Matrix:");
            System.Diagnostics.Debug.WriteLine($"[{_transformMatrix.M11:F3}, {_transformMatrix.M12:F3}]");
            System.Diagnostics.Debug.WriteLine($"[{_transformMatrix.M21:F3}, {_transformMatrix.M22:F3}]");

            // 验证标准向量的变换结果
            DbPt standardX = new DbPt(1, 0);
            DbPt standardY = new DbPt(0, 1);

            DbPt transformedX = GetTransformedDirection(standardX);
            DbPt transformedY = GetTransformedDirection(standardY);

            System.Diagnostics.Debug.WriteLine($"Standard X (1,0) -> ({transformedX.X:F3}, {transformedX.Y:F3})");
            System.Diagnostics.Debug.WriteLine($"Standard Y (0,1) -> ({transformedY.X:F3}, {transformedY.Y:F3})");
        }

        /// <summary>
        /// 测试方法：验证镜像后的几何体是否正确
        /// </summary>
        public void TestMirrorGeometry()
        {
            // 在Activate方法末尾调用此方法进行验证
            if (_hasTransform)
            {
                // 获取变换后的方向向量
                DbPt rightDir = GetTransformedDirection(new DbPt(1, 0));
                DbPt upDir = GetTransformedDirection(new DbPt(0, 1));

                // 验证方向向量是否正交且长度为1
                double rightLength = Math.Sqrt(rightDir.X * rightDir.X + rightDir.Y * rightDir.Y);
                double upLength = Math.Sqrt(upDir.X * upDir.X + upDir.Y * upDir.Y);
                double dotProduct = rightDir.X * upDir.X + rightDir.Y * upDir.Y;

                System.Diagnostics.Debug.WriteLine($"Right direction length: {rightLength:F3} (should be ~1.0)");
                System.Diagnostics.Debug.WriteLine($"Up direction length: {upLength:F3} (should be ~1.0)");
                System.Diagnostics.Debug.WriteLine($"Dot product: {dotProduct:F3} (should be ~0.0 for orthogonal)");

                // 验证控制点位置
                if (ConPts.Count >= 2)
                {
                    DbPt bottomCenter = ConPts[0];
                    DbPt geometryCenter = ConPts[1];

                    double distance = Math.Sqrt(
                        Math.Pow(geometryCenter.X - bottomCenter.X, 2) +
                        Math.Pow(geometryCenter.Y - bottomCenter.Y, 2)
                    );

                    System.Diagnostics.Debug.WriteLine($"Control points distance: {distance:F3} (should be {_height/2.0:F3})");
                }
            }
        }

        #endregion

        #region 数据持久化
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter"></param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //图元类标识，必要
            binaryWriter.Write(this.GetType().ToString());

            //版本号，必要
            binaryWriter.Write(0);

            //图元共有参数，必要
            PubSave(binaryWriter);

            //图元特有参数
            binaryWriter.Write(_width);
            binaryWriter.Write(_height);
            binaryWriter.Write(_thickness);
            binaryWriter.Write(_hoAngle);
            binaryWriter.Write(_ifHatch);

            // 保存变换矩阵
            binaryWriter.Write(_hasTransform);
            if (_hasTransform)
            {
                binaryWriter.Write(_transformMatrix.M11);
                binaryWriter.Write(_transformMatrix.M12);
                binaryWriter.Write(_transformMatrix.M21);
                binaryWriter.Write(_transformMatrix.M22);
            }
            binaryWriter.Write(_ifShowCenterLine);
        }

        /// <summary>
        /// 装载数据
        /// </summary>
        /// <param name="binaryReader"></param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            int verNum = binaryReader.ReadInt32();

            //图元共有参数
            PubLoad(binaryReader);

            if (verNum == 0)
            {
                // 版本1：不包含偏移参数
                _width = binaryReader.ReadDouble();
                _height = binaryReader.ReadDouble();
                _thickness = binaryReader.ReadDouble();
                _hoAngle = binaryReader.ReadDouble();
                _ifHatch = binaryReader.ReadBoolean();

                // 加载变换矩阵
                _hasTransform = binaryReader.ReadBoolean();
                if (_hasTransform)
                {
                    double m11 = binaryReader.ReadDouble();
                    double m12 = binaryReader.ReadDouble();
                    double m21 = binaryReader.ReadDouble();
                    double m22 = binaryReader.ReadDouble();
                    _transformMatrix = new Matrix3D(
                        m11, m12, 0, 0,
                        m21, m22, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1
                    );
                }
                else
                {
                    _transformMatrix = Matrix3D.Identity;
                }
            }

            // 数据加载后需要重新计算下边中点和几何中心
            _isGeometryOperation = false; // 数据加载不是几何操作
            _ifShowCenterLine = binaryReader.ReadBoolean();
        }

        /// <summary>
        /// 深度复制
        /// </summary>
        public override DbElement EleCopy(bool changeUid = false)
        {
            SteelTubePlanD ele = new SteelTubePlanD();

            // 图元共有参数
            PubCopy(ele);

            // 图元特有参数
            ele._width = _width;
            ele._height = _height;
            ele._thickness = _thickness;
            ele._hoAngle = _hoAngle;
            ele._ifHatch = _ifHatch;

            // 复制核心位置点
            ele._insertPt = _insertPt.EleCopy();
            ele._bottomCenterPt = _bottomCenterPt.EleCopy();
            ele._centerPt = _centerPt.EleCopy();

            // 复制变换矩阵
            ele._hasTransform = _hasTransform;
            ele._transformMatrix = _transformMatrix;

            // 操作状态不复制，确保每个图元都有独立的状态
            ele._isGeometryOperation = false; // 复制后不是几何操作

            ele._ifShowCenterLine = _ifShowCenterLine;

            if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }

            return ele;
        }

        #endregion
        #endregion



}
